package my.com.cmg.phis.patient.v2.repository;

import static org.jooq.impl.DSL.*;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.phis.core.constant.RefCodeConstant;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.util.CommonUtil;
import my.com.cmg.phis.core.util.DateUtil;
import my.com.cmg.phis.core.util.TableUtil;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListDTO;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListRequestDTO;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.DatePart;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.SelectSeekStep1;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class PatientVisitManagementRepositoryJooq {
  private final DSLContext dsl;

  public List<PatientVisitListDTO> getPatientVisitList(
      PatientVisitListRequestDTO requestDTO, PaginationRequestDTO pg, Long userId) {
    Condition condition = noCondition();

    // Base condition
    condition =
        condition.and(
            field("PV.visit_type")
                .in(
                    RefCodeConstant.VISIT_TYPE_INPATIENT,
                    RefCodeConstant.VISIT_TYPE_EMERGENCY,
                    RefCodeConstant.VISIT_TYPE_OUTPATIENT,
                    RefCodeConstant.VISIT_TYPE_DAYCARE,
                    RefCodeConstant.VISIT_TYPE_PAC)
                .and(field("PV.visit_checkout").eq(RefCodeConstant.BOOLEAN_NO))
                .and(field("PV.provision_discharge").eq(RefCodeConstant.BOOLEAN_NO))
                .and(field("PV.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString())));
    condition =
        condition.and(
            field("PP.active_flag")
                .eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString())
                .and(field("PP.merge_to_patient_seqno").isNull()));

    // Default location
    if (!CommonUtil.isExist(requestDTO.visitLocation())) {
      condition = condition.and(field("PV.visit_location_seqno").in(getUserLocations(userId)));
    }

    // Filter by Visit Type [Outpatient, Daycare]
    LocalDateTime midnight = DateUtil.getEndOfDay(LocalDateTime.now());
    condition =
        condition.and(
            DSL.or(
                field("PV.visit_type")
                    .notIn(
                        RefCodeConstant.VISIT_TYPE_OUTPATIENT, RefCodeConstant.VISIT_TYPE_DAYCARE),
                field("PV.visit_date").le(midnight)));

    // Filter by Visit Type [Emergency, PAC]
    LocalDateTime currentDate = LocalDateTime.now();
    condition =
        condition.and(
            DSL.or(
                field("PV.visit_type")
                    .notIn(RefCodeConstant.VISIT_TYPE_EMERGENCY, RefCodeConstant.VISIT_TYPE_PAC),
                field("PV.visit_date").add(1, DatePart.DAY).le(midnight)));
    return null;
  }

  private List<Long> getUserLocations(Long userId) {
    Condition condition = noCondition();
    condition = condition.and(field("SU.usr_id").eq(userId));
    condition =
        condition.and(field("LS.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));

    Field<Long> locSetupSeqno = field("UL.loc_setup_seqno", Long.class);
    SelectSeekStep1<Record1<Long>, Object> query =
        dsl.selectDistinct(locSetupSeqno)
            .from(TableUtil.table(TableUtil.PH_USER_LOCATIONS, "UL"))
            .leftJoin(TableUtil.table(TableUtil.PH_SEC_USER, "SU"))
            .on(field("UL.usr_id").eq(field("SU.usr_id")))
            .leftJoin(TableUtil.table(TableUtil.PH_LOCATION_SETUP, "LS"))
            .on(field("UL.loc_setup_seqno").eq(field("LS.loc_setup_seqno")))
            .where(condition)
            .orderBy(field("UL.loc_setup_seqno"));

    return query.fetchInto(Long.class);
  }
}
