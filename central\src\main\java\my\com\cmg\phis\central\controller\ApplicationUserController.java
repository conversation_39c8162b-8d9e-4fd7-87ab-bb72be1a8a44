package my.com.cmg.phis.central.controller;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.phis.central.service.ICentralAppUserService;
import my.com.cmg.phis.centralCore.dto.ApplicationUser.ActiveFacilityDTO;
import my.com.cmg.phis.centralCore.dto.ApplicationUser.AppUserActiveApplicationDTO;
import my.com.cmg.phis.centralCore.dto.ApplicationUser.ApplicationTypeDTO;
import my.com.cmg.phis.centralCore.dto.RefCodesDTO;
import my.com.cmg.phis.centralCore.model.SecurityUser;
import my.com.cmg.phis.centralCore.util.LogUtil;
import org.springframework.boot.actuate.web.exchanges.HttpExchange.Principal;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/api/v1/central/app_user")
public class ApplicationUserController {
  private final ICentralAppUserService centralAppUserService;

  /**
   * Retrieves a list of facilities based on the provided parameters.
   *
   * @param userId Optional user ID to filter facilities for a specific user.
   * @param size Optional size parameter to limit the number of facilities returned.
   * @param facilityType Optional facility type to filter the selection.
   * @param authentication Authentication object containing the user's security details.
   * @return A list of RefCodesDTO representing the facilities available for the user.
   */
  @GetMapping("/facilities/selection")
  public List<RefCodesDTO> getFacilitySelection(
      @RequestParam(required = false) String userId,
      @RequestParam(required = false) Long size,
      @RequestParam(required = false) String facilityType,
      Authentication authentication) {
    log.info(LogUtil.ENTRY, "getFacilities");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getFacilitySelection(size, facilityType, user.getKeycloakUserId());
  }

  /**
   * Retrieves a list of applications that the user has a pending request for. This is typically
   * used to display the list of applications that the user has requested access to, but has not yet
   * been approved.
   *
   * @param authentication Authentication object containing the user's security details.
   * @return A list of AppUserActiveApplicationDTO representing the applications that the user has a
   *     pending request for.
   */
  @GetMapping("/applications/pending")
  public List<AppUserActiveApplicationDTO> getPendingApplications(Authentication authentication) {
    log.info(LogUtil.ENTRY, "getPendingApplications");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getPendingApplications(user.getEmail());
  }

  /**
   * Retrieves a list of applications that the user currently has active access to.
   *
   * @param authentication Authentication object containing the user's security details.
   * @return A list of AppUserActiveApplicationDTO representing the applications that the user
   *     currently has active access to.
   */
  @GetMapping("/applications/active")
  public List<AppUserActiveApplicationDTO> getActiveApplications(
      Authentication authentication, @RequestParam(required = false) String facilityLocation) {
    log.info(LogUtil.ENTRY, "getActiveApplications with facilityLocation: {}", facilityLocation);
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getActiveApplications(user.getKeycloakUserId(), facilityLocation);
  }

  @GetMapping("/application/list")
  public List<ApplicationTypeDTO> getApplicationsByType(
      Authentication authentication, @RequestParam(required = false) String type) {
    log.info(LogUtil.ENTRY, "getApplicationList");
    return centralAppUserService.getApplicationsByType(type);
  }

  @GetMapping("/application/non_government")
  public List<RefCodesDTO> getApplicationsByNonGov(Authentication authentication) {
    log.info(LogUtil.ENTRY, "getApplicationList");
    return centralAppUserService.getApplicationsByNonGov();
  }

  /**
   * Retrieves a list of applications that the user currently does not have active access to.
   *
   * @param authentication Authentication object containing the user's security details.
   * @param principal Principal object representing the authenticated principal.
   * @return A list of AppUserActiveApplicationDTO representing the applications that the user
   *     currently does not have active access to.
   */
  @GetMapping("/applications/inactive")
  public List<AppUserActiveApplicationDTO> getInactiveApplications(
      Authentication authentication, Principal principal) {
    log.info(LogUtil.ENTRY, "getInactiveApplications");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getInactiveApplications(user.getKeycloakUserId());
  }

  /**
   * Accepts a pending application request for the given application id.
   *
   * @param appId The id of the application to accept.
   * @param authentication The authentication object containing the user's security details.
   */
  @PostMapping("/applications/accept/{appId}")
  public void acceptApplications(@PathVariable("appId") Long appId, Authentication authentication) {
    log.info(LogUtil.ENTRY, "acceptApplications");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    centralAppUserService.acceptApplications(appId, user.getKeycloakUserId());
  }

  /**
   * Rejects a pending application request for the given application id.
   *
   * @param appId The id of the application to reject.
   * @param authentication The authentication object containing the user's security details.
   */
  @PostMapping("/applications/reject/{appId}")
  public void rejectApplications(@PathVariable("appId") Long appId, Authentication authentication) {
    log.info(LogUtil.ENTRY, "rejectApplications");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    centralAppUserService.rejectApplications(appId, user.getKeycloakUserId());
  }

  /**
   * Activates an application request for the given application id.
   *
   * @param id The id of the application to activate.
   */
  @PostMapping("/applications/active/{id}")
  public void activeApplications(@PathVariable("id") Long id) {
    log.info(LogUtil.ENTRY, "activeApplications");
    centralAppUserService.activeApplications(id);
  }

  /**
   * Deactivates an application request for the given application id.
   *
   * @param id The id of the application to deactivate.
   */
  @PostMapping("/applications/inactive/{id}")
  public void inactiveApplications(@PathVariable("id") Long id) {
    log.info(LogUtil.ENTRY, "inactiveApplications");
    centralAppUserService.inactiveApplications(id);
  }

  /**
   * Checks if the current user is a super admin or not.
   *
   * @param authentication The authentication object containing the user's security details.
   * @return True if the user is a super admin, false otherwise.
   */
  @GetMapping("/super_admin")
  public Boolean userIsSuperAdmin(Authentication authentication) {
    log.info(LogUtil.ENTRY, "userIsSuperAdmin");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getIsSuperAdmin(user.getKeycloakUserId());
  }

  /**
   * Checks if the current user is an application admin or not.
   *
   * @param authentication The authentication object containing the user's security details.
   * @return True if the user is an application admin, false otherwise.
   */
  @GetMapping("/app_admin")
  public Boolean userIsAppAdmin(Authentication authentication) {
    log.info(LogUtil.ENTRY, "userIsAppAdmin");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getIsAppAdmin(user.getKeycloakUserId());
  }

  /**
   * Retrieves a list of active facilities for the given application id.
   *
   * @param applicationId The id of the application.
   * @param authentication The authentication object containing the user's security details.
   * @return A list of ActiveFacilityDTO representing the active facilities for the given
   *     application.
   */
  @GetMapping("/applications/{applicationId}/facilities")
  public List<ActiveFacilityDTO> getActiveFacilities(
      @PathVariable("applicationId") Long applicationId, Authentication authentication) {
    log.info(LogUtil.ENTRY, "getActiveFacilities");
    SecurityUser user = (SecurityUser) authentication.getPrincipal();
    return centralAppUserService.getActiveFacilities(applicationId, user.getKeycloakUserId());
  }
}
