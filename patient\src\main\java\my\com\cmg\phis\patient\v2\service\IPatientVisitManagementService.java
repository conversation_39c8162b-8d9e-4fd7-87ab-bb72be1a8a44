package my.com.cmg.phis.patient.v2.service;

import java.util.List;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.security.SecUserDTO;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListDTO;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListRequestDTO;

public interface IPatientVisitManagementService {
  List<PatientVisitListDTO> getPatientVisitList(
      PatientVisitListRequestDTO requestDTO,
      PaginationRequestDTO paginationRequestDTO,
      SecUserDTO user);
}
