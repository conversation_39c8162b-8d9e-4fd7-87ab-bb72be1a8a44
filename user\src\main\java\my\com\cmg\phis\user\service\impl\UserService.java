package my.com.cmg.phis.user.service.impl;

import static org.jooq.impl.DSL.user;

import jakarta.transaction.Transactional;
import java.security.Principal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.phis.centralCore.dto.RefCodesDTO;
import my.com.cmg.phis.centralCore.dto.user.RegisterUserDTO;
import my.com.cmg.phis.centralCore.dto.user.UserDTOJooq;
import my.com.cmg.phis.centralCore.dto.user.UserDetailsDTO;
import my.com.cmg.phis.centralCore.dto.user.UserProfileDTO;
import my.com.cmg.phis.centralCore.exception.ExceptionCode;
import my.com.cmg.phis.centralCore.exception.PhisException;
import my.com.cmg.phis.centralCore.model.ApplicationFacility;
import my.com.cmg.phis.centralCore.model.FacilityUser;
import my.com.cmg.phis.centralCore.model.User;
import my.com.cmg.phis.centralCore.repository.applicationAdmin.jpa.ApplicationFacilityAdminRepository;
import my.com.cmg.phis.centralCore.repository.facilityUser.jpa.FacilityUserRepository;
import my.com.cmg.phis.centralCore.repository.user.jooq.UserRepositoryJooq;
import my.com.cmg.phis.centralCore.repository.user.jpa.UserRepository;
import my.com.cmg.phis.centralCore.service.impl.EmailService;
import my.com.cmg.phis.centralCore.util.CommonUtil;
import my.com.cmg.phis.centralCore.util.PhisUtils;
import my.com.cmg.phis.user.dto.PaginationResponseDTO;
import my.com.cmg.phis.user.dto.user.RegisterationDTO;
import my.com.cmg.phis.user.dto.user.ResetPasswordDTO;
import my.com.cmg.phis.user.dto.user.UsersRequestDTO;
import my.com.cmg.phis.user.dto.user.UsersUpdateDTOJooq;
import my.com.cmg.phis.user.mapper.user.UsersMapper;
import my.com.cmg.phis.user.service.IUserService;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserService implements IUserService {

  final UserRepository userRepository;
  final KeycloakService keycloakService;
  final EmailService emailService;
  private FacilityUserRepository facilityUserRepository;
  private UserRepositoryJooq userRepositoryJooq;
  private final ApplicationFacilityAdminRepository applicationFacilityAdminRepository;

  @Value("${phis-central-url}")
  String phisCentralUrl;

  public UserService(
      UserRepository userRepository,
      KeycloakService keycloakService,
      UserRepositoryJooq userRepositoryJooq,
      FacilityUserRepository facilityUserRepository,
      EmailService emailService,
      ApplicationFacilityAdminRepository applicationFacilityAdminRepository) {
    this.userRepository = userRepository;
    this.keycloakService = keycloakService;
    this.emailService = emailService;
    this.userRepositoryJooq = userRepositoryJooq;
    this.facilityUserRepository = facilityUserRepository;
    this.applicationFacilityAdminRepository = applicationFacilityAdminRepository;
  }

  @Override
  public RegisterUserDTO getUserProfile(String keycloakUserId) {
    return userRepository.findByKeycloakUserId(keycloakUserId);
  }

  @Override
  public List<RefCodesDTO> getIdType() {
    return userRepositoryJooq.getIdType();
  }

  @Override
  public List<RefCodesDTO> getUserGovernmentType() {
    return userRepositoryJooq.getUserGovernmentType();
  }

  @Override
  public List<RefCodesDTO> getUserGovType() {
    return userRepositoryJooq.getUserGovType();
  }

  @Override
  public List<RefCodesDTO> getUserNonGovernmentType(String application) {
    return userRepositoryJooq.getUserNonGovernmentType(application);
  }

  @Override
  public List<RefCodesDTO> getSpeciality() {
    return userRepositoryJooq.getSpeciality();
  }

  @Override
  public List<RefCodesDTO> getDivision() {
    return userRepositoryJooq.getDivision();
  }

  @Override
  public List<RefCodesDTO> getDepartment() {
    return userRepositoryJooq.getDepartment();
  }

  @Override
  public List<RefCodesDTO> getProfessionalType() {
    return userRepositoryJooq.getProfessionalType();
  }

  @Override
  public List<RefCodesDTO> getAgency() {
    return userRepositoryJooq.getAgency();
  }

  @Override
  public List<RefCodesDTO> getSupplier() {
    return userRepositoryJooq.getSupplier();
  }

  @Override
  public List<RefCodesDTO> getFacility() {
    return userRepositoryJooq.getFacility();
  }

  @Override
  public void updateUserProfile(String keycloakUserId, UserProfileDTO dto) {
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    u.setAddress(dto.address());
    u.setName(dto.name());
    u.setContactNo(dto.contactNo());
    u.setIdNo(dto.idNo());
    u.setUsrHospClinicId(dto.usrHospClinicId());
    u.setUsrSuperior(dto.usrSuperior());
    u.setUsrDesignation(dto.usrDesignation());
    u.setUpdatedDate(LocalDateTime.now());

    keycloakService.updateUserProfile(keycloakUserId, dto);
    userRepository.save(u);
  }

  @Override
  @Transactional
  public void forgetPassword(String email) {
    User u = userRepository.findByEmail(email);

    if (u == null) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid email!");
    }

    if (u.getPasswordKey() == null) {
      String rs = RandomStringUtils.randomAlphanumeric(16);
      u.setPasswordKey(rs);

      userRepository.save(u);
    }
    sendForgetPasswordEmailToUser(u);
  }

  private void sendForgetPasswordEmailToUser(User u) {
    emailService.sendForgetPasswordEmail(
        u.getEmail(), u.getName(), phisCentralUrl + "/reset_password?token=" + u.getPasswordKey());
  }

  @Override
  public boolean verifyResetToken(String token) {
    User u = userRepository.findByPasswordKey(token);
    if (u != null) {
      return true;
    }
    throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid token!");
  }

  @Override
  public void resetPassword(ResetPasswordDTO dto) {
    User u = userRepository.findByPasswordKey(dto.token());
    if (u == null) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid token!");
    }
    keycloakService.changePassword(u.getKeycloakUserId(), "", dto.password());
    u.setPasswordKey(null);
    userRepository.save(u);
  }

  @Override
  public void createUser(RegisterationDTO registerUserDTO) {
    String username =
        CommonUtil.isEmpty(registerUserDTO.username())
            ? registerUserDTO.idNo()
            : registerUserDTO.username();
    // check if user exists
    checkUserExistsByEmailAndUsername(registerUserDTO.email(), username);
    // get created user from keycloak
    String keycloakUserId = keycloakService.getUserIdFromKeycloak(username);
    // create user in keycloak
    if (!CommonUtil.isEmpty(keycloakUserId)) {
      System.out.println("User Exist");
      keycloakService.deleteUserInKeycloak(keycloakUserId);
    }
    keycloakService.createUserInKeycloak(
        username, registerUserDTO.email(), registerUserDTO.password(), registerUserDTO.name());
    System.out.println("-------------Created user in keycloak");
    // get created user from keycloak
    keycloakUserId = keycloakService.getUserIdFromKeycloak(username);

    // 03/Jun/2025: Currently username is same as use idNo
    // Insert user to database
    User u =
        User.builder()
            .email(registerUserDTO.email())
            .username(username)
            .residentType(registerUserDTO.residentType())
            .name(registerUserDTO.name())
            .idNo(registerUserDTO.idNo())
            .idTypeCode(registerUserDTO.idTypeCode())
            .email(registerUserDTO.email())
            .contactNo(registerUserDTO.contactNo())
            .keycloakUserId(keycloakUserId)
            .activeFlag("A")
            .isVerified(false)
            .isSuperAdmin(false)
            .createdDate(LocalDateTime.now())
            .updatedDate(LocalDateTime.now())
            .build();
    try {
      userRepository.save(PhisUtils.checkNotNull(u));
    } catch (Exception e) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Failed to create user");
    }
  }

  private void sendVerificationEmailToUser(User u) {
    emailService.sendVerificationEmail(
        u.getEmail(),
        u.getName(),
        phisCentralUrl + "/verification?token=" + u.getVerificationKey());
  }

  // @Override
  // public void resendVerificationEmail(String keycloakUserId) {
  //   User u = userRepository.getByKeycloakUserId(keycloakUserId);
  //   if (u == null) {
  //     throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid user!");
  //   }
  //   sendVerificationEmailToUser(u);
  // }

  @Override
  @Transactional
  public void resendVerificationEmail(String keycloakUserId) {
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    if (u == null) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid user!");
    }
    String newVerificationKey = RandomStringUtils.randomAlphanumeric(16);
    u.setVerificationKey(newVerificationKey);

    LocalDateTime newExpiry = LocalDateTime.now().plusDays(1);
    u.setVerifyKeyExpiry(newExpiry);

    try {
      userRepository.save(PhisUtils.checkNotNull(u));
    } catch (Exception e) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Failed to update user verification key");
    }

    // Send verification email with new key
    sendVerificationEmailToUser(u);
  }

  @Transactional
  @Override
  public void completeProfile(Principal principal, UserDetailsDTO userDetailDTO) {
    String keycloakUserId = principal.getName();
    User user = userRepository.getByKeycloakUserId(keycloakUserId);
    user.setUsrDesignation(userDetailDTO.userDesignation());
    user.setFacilityType(userDetailDTO.facilityType());
    user.setLocationType(userDetailDTO.locationType());
    user.setUserTypeCode(userDetailDTO.userTypeCode());
    user.setDivision(userDetailDTO.division());
    user.setDepartment(userDetailDTO.department());
    user.setProfessionalRegistrationType(userDetailDTO.professionalRegistrationType());
    user.setRegistrationNo(userDetailDTO.registrationNo());
    user.setSpecialty(userDetailDTO.specialty());
    user.setLicenseNo(userDetailDTO.licenseNo());
    user.setLicenseDate(userDetailDTO.licenseDate());
    user.setLicenceExpiry(userDetailDTO.licenceExpiry());
    user.setKeycloakUserId(keycloakUserId);
    user.setRequestDate(LocalDateTime.now());
    user.setUpdatedDate(LocalDateTime.now());

    UserProfileDTO userProfileDTO =
        new UserProfileDTO(user.getName(), null, null, null, null, null, null, null, null);
    keycloakService.updateUserProfile(keycloakUserId, userProfileDTO);
    // Call userDetailsApp
    userDetailsApp(user, userDetailDTO, keycloakUserId);
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    u.setCompleted(true);
    userRepository.save(PhisUtils.checkNotNull(user));
  }

  public void userDetailsApp(User user, UserDetailsDTO dto, String keycloakUserId) {
    if ("FACILITY".equals(dto.locationType())) {
      Long applicationFacilitySeqno =
          userRepositoryJooq.getApplicationFacilitySeqno(dto.facilitySeqno());
      ApplicationFacility applicationFacility =
          applicationFacilityAdminRepository.getReferenceById(applicationFacilitySeqno);
      FacilityUser facilityUser = new FacilityUser();
      facilityUser.setApplicationFacility(applicationFacility);
      facilityUser.setUserEmail(user.getEmail());
      facilityUser.setUserId(keycloakUserId);
      facilityUser.setIsAdmin(false);
      facilityUser.setIsActive(false);
      facilityUserRepository.save(facilityUser);

    } else if (dto.applicationSeqno() != null && dto.applicationSeqno().size() > 0) {
      List<FacilityUser> facilityUsers = new ArrayList<>();
      dto.applicationSeqno()
          .forEach(
              app -> {
                FacilityUser facilityUser = new FacilityUser();
                facilityUser.setApplicationSeqno(app);
                facilityUser.setUserEmail(user.getEmail());
                facilityUser.setUserId(keycloakUserId);
                facilityUser.setIsAdmin(false);
                facilityUser.setIsActive(false);
                facilityUsers.add(facilityUser);
              });
      facilityUserRepository.saveAll(facilityUsers);
    } else {
      throw new PhisException(ExceptionCode.INFORM, "Please select at least one application");
    }
  }

  @Override
  public Boolean getUserStatus(String keycloakUserId) {
    Boolean isVerified = getUserIsVerified(keycloakUserId);
    if (!isVerified) {
      throw new PhisException(ExceptionCode.FORBIDDEN, "User is not verified");
    } else {
      Boolean isCompleted = getUserIsCompletedAttribute(keycloakUserId);
      if (!isCompleted) {
        throw new PhisException(ExceptionCode.BAD_REQUEST, "User has not completed user profile");
      }
    }
    return true;
  }

  private void checkUserExistsByEmailAndUsername(String email, String username)
      throws PhisException {

    List<User> users = userRepository.findByEmailOrUsername(email, username);

    users.forEach(
        user -> {
          System.out.println("User found in database");
          System.out.println(user);

          if (user != null) {
            // check if email in users if yes throw exception
            if (!user.getEmail().isEmpty() && user.getEmail().equals(email)) {
              System.out.println("Email already exists!");
              throw new PhisException(ExceptionCode.BAD_REQUEST, "Email already exists!");
            }
            // check if username in users if yes throw exception
            if (user.getUsername().equals(username)) {
              System.out.println("Username already exists!");
              throw new PhisException(ExceptionCode.BAD_REQUEST, "Username already exists!");
            }
          }
        });
  }

  // private void checkUserExistsByIdNo(String idNo) throws PhisException {

  //   List<User> users = userRepository.findByIdNo(idNo);

  //   if (users.size() > 0) {
  //     throw new PhisException(ExceptionCode.BAD_REQUEST, "Identification number already
  // exists!");
  //   }
  // }

  @Override
  public void changePassword(String keycloakUserId, String oldPassword, String newPassword) {
    keycloakService.changePassword(keycloakUserId, oldPassword, newPassword);
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    emailService.sendPasswordUpdateEmail(u.getEmail(), u.getName());
  }

  @Override
  @Transactional
  public String updateUserIsCompletedAttribute(String keycloakUserId) {
    log.info("updateUserIsCompletedAttribute keycloakUserId: {}", keycloakUserId);
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    u.setCompleted(true);
    userRepository.save(u);
    return "success";
  }

  @Override
  @Transactional
  public Boolean getUserIsCompletedAttribute(String keycloakUserId) {
    log.info("getUserIsCompletedAttribute keycloakUserId: {}", keycloakUserId);
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    return u.isCompleted();
  }

  @Override
  @Transactional
  public Boolean getUserIsVerified(String keycloakUserId) {
    log.info("getUserIsCompletedAttribute keycloakUserId: {}", keycloakUserId);
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    return u.isVerified();
  }

  @Override
  @Transactional
  public String updateUserIsVerifiedAttribute(String keycloakUserId, String token) {
    verifyEmailToken(token);
    keycloakService.updateUserIsVerifiedAttribute(
        keycloakUserId, true, keycloakService.getUserIsVerifiedAttribute(keycloakUserId));
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    u.setVerified(true);
    userRepository.save(u);
    return "success";
  }

  @Override
  public boolean verifyEmailToken(String token) {
    User u = userRepository.findByVerificationKey(token);
    if (u != null) {
      if (u.getVerifyKeyExpiry() == null || u.getVerifyKeyExpiry().isAfter(LocalDateTime.now())) {
        return true;
      } else {
        throw new PhisException(ExceptionCode.BAD_REQUEST, "Token has expired!");
      }
    } else {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid token!");
    }
  }

  @Override
  public String getUserIsVerifiedAttribute(String keycloakUserId) {
    User u = userRepository.getByKeycloakUserId(keycloakUserId);
    if (u == null) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid user ID");
    }

    return keycloakService.getUserIsVerifiedAttribute(keycloakUserId);
  }

  @Override
  public void updateUserEnabledAttribute(Long id, boolean enabled) {
    User u = userRepository.findById(PhisUtils.checkNotNull(id)).orElse(null);
    if (u == null) {
      throw new PhisException(ExceptionCode.BAD_REQUEST, "Invalid user ID");
    }

    System.err.println("GetUser:" + u.toString());
    keycloakService.updateUserEnabled(u.getKeycloakUserId(), enabled);
    userRepository.updateUserEnabled(enabled ? "A" : "I", u.getId());
  }

  @Override
  public UserDTOJooq getUser(Long id) {
    return userRepositoryJooq.getRow(id);
  }

  @Override
  public List<UserDTOJooq> getUsers(UsersRequestDTO req) {
    UsersMapper mapper = new UsersMapper();
    String sort = mapper.getColumnName(req.sort());
    Long pg = req.page() != null ? req.page() : 1L;
    // Long offset = (pg - 1) * 10;
    String sortDirection = "asc";
    if (req.sortDirection() != null && req.sortDirection().contains("desc")) {
      sortDirection = "desc";
    }
    // String sortDirection = req.sortDirection().contains("desc") ? "desc" : "asc";
    Pageable p =
        PageRequest.of(pg.intValue() - 1, 10, Sort.Direction.fromString(sortDirection), sort);
    System.out.println("page: " + p);
    // System.out.println("sortDirectionReq: " + (req.sortDirection() == "asc" ||
    // req.sortDirection().contains("desc")));
    String id = req.id() != null ? req.id() : null;
    String name = req.name() != null ? req.name() : null;

    return userRepositoryJooq.getList(id, name, sort, sortDirection, pg);
  }

  public PaginationResponseDTO getUsersPage(UsersRequestDTO req) {
    String id = req.id() != null ? req.id() : null;
    String name = req.name() != null ? req.name() : null;
    Long total = userRepositoryJooq.getListPages(id, name);
    System.out.println("i: " + total);
    Long size = 10L;
    Long totalPages = total / size;
    if (total % size > 0) {
      totalPages++;
    }
    return new PaginationResponseDTO(totalPages, total, size);
  }

  @Override
  public void updateUsers(UsersUpdateDTOJooq dto) {
    User user = userRepository.getReferenceById(PhisUtils.checkNotNull(dto.id()));
    boolean check = false;
    if (dto.isSuperAdmin().equals("true")) {
      check = true;
    }
    if (!dto.status().equals(user.getActiveFlag())) {
      updateUserEnabledAttribute(dto.id(), dto.status().equals("A"));
    }
    user.setSuperAdmin(check);
    user.setActiveFlag(dto.status());
    userRepository.save(user);
  }

  @Override
  public List<RefCodesDTO> getDesignation() {
    return userRepositoryJooq.getDesignationList();
  }
}
