package my.com.cmg.phis.patient.v2.service;

import java.util.List;
import my.com.cmg.phis.core.dto.RefCodesDTO;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.dto.paging.PaginationResponseDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListRequestDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DisciplineSubdisciplineDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DoctorListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListRequestDTO;

public interface IUtilsService {
  List<CityListDTO> getCityLists(
      CityListRequestDTO requestDTO, PaginationRequestDTO paginationRequestDTO);

  PaginationResponseDTO getCityListPages(
      CityListRequestDTO requestDTO, PaginationRequestDTO paginationRequestDTO);

  List<OccupationListDTO> getOccupationLists(
      OccupationListRequestDTO requestDTO, PaginationRequestDTO paginationRequestDTO);

  PaginationResponseDTO getOccupationListPages(
      OccupationListRequestDTO requestDTO, PaginationRequestDTO paginationRequestDTO);

  List<RefCodesDTO> getSalutations();

  List<RefCodesDTO> getGenders();

  List<RefCodesDTO> getIdTypes();

  List<RefCodesDTO> getOtherIdTypes();

  List<RefCodesDTO> getRaces();

  List<RefCodesDTO> getReligions();

  List<RefCodesDTO> getNationalities();

  List<RefCodesDTO> getPaymentCategories();

  List<RefCodesDTO> getGovernmentRelationships();

  List<RefCodesDTO> getPensionerCategories();

  List<RefCodesDTO> getStates();

  List<RefCodesDTO> getMaritalStatus();

  List<RefCodesDTO> getEducationalLevels();

  List<RefCodesDTO> getNextOfKinIdTypes();

  List<RefCodesDTO> getRelationships();

  List<RefCodesDTO> getHouseholdIncomes();

  List<RefCodesDTO> getPatientStatus();

  List<DisciplineSubdisciplineDTO> getDisciplineSubdiscipline();

  List<DoctorListDTO> getDoctorList(Long subdisciplineSeqno);
}
