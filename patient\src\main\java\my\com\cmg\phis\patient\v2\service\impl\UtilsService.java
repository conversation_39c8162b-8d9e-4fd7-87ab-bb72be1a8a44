package my.com.cmg.phis.patient.v2.service.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.phis.core.constant.RefCodeConstant;
import my.com.cmg.phis.core.dto.RefCodesDTO;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.dto.paging.PaginationResponseDTO;
import my.com.cmg.phis.core.repository.Jooq.CoreUtilsRepositoryJooq;
import my.com.cmg.phis.core.util.PaginationUtil;
import my.com.cmg.phis.patient.v2.dto.utils.CityListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListRequestDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DisciplineSubdisciplineDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DoctorListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListRequestDTO;
import my.com.cmg.phis.patient.v2.mapper.utils.CityListMapper;
import my.com.cmg.phis.patient.v2.mapper.utils.OccupationListMapper;
import my.com.cmg.phis.patient.v2.repository.UtilsRepositoryJooq;
import my.com.cmg.phis.patient.v2.service.IUtilsService;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service("UtilsServiceV2")
public class UtilsService implements IUtilsService {
  private final UtilsRepositoryJooq utilsRepositoryJooq;
  private final CoreUtilsRepositoryJooq coreUtilsRepositoryJooq;

  /**
   * Retrieves a list of cities based on the provided request and pagination details.
   *
   * @param requestDTO The request criteria containing filters for town code, town description, and
   *     state code.
   * @param pgDTO The pagination request details including sorting information.
   * @return A list of CityListDTO objects that match the specified criteria and pagination.
   */
  @Override
  public List<CityListDTO> getCityLists(CityListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new CityListMapper(), false);
    List<CityListDTO> cityLists = utilsRepositoryJooq.getCityLists(requestDTO, pg);
    return cityLists;
  }

  /**
   * Retrieves the total number of city records that match the given request criteria.
   *
   * @param requestDTO the request criteria containing filters for town code, town description, and
   *     state code
   * @param pgDTO the pagination request details including sorting information
   * @return a PaginationResponseDTO object containing the total number of city records that match
   *     the specified criteria and pagination.
   */
  @Override
  public PaginationResponseDTO getCityListPages(
      CityListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    Long total = utilsRepositoryJooq.getCityListPages(requestDTO);
    PaginationResponseDTO response = PaginationUtil.pagination(pgDTO.size(), total);
    return response;
  }

  /**
   * Retrieves a list of occupations based on the provided request and pagination details.
   *
   * @param requestDTO The request criteria containing filters for occupation code and description.
   * @param pgDTO The pagination request details including sorting information.
   * @return A list of OccupationListDTO objects that match the specified criteria and pagination.
   */
  @Override
  public List<OccupationListDTO> getOccupationLists(
      OccupationListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new OccupationListMapper(), false);
    List<OccupationListDTO> occupationLists =
        utilsRepositoryJooq.getOccupationLists(requestDTO, pg);
    return occupationLists;
  }

  /**
   * Retrieves the total number of occupation records that match the given request criteria.
   *
   * @param requestDTO the request criteria containing filters for occupation code and description
   * @param pgDTO the pagination request details including sorting information
   * @return a PaginationResponseDTO object containing the total number of occupation records that
   *     match the specified criteria and pagination
   */
  @Override
  public PaginationResponseDTO getOccupationListPages(
      OccupationListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    Long total = utilsRepositoryJooq.getOccupationListPages(requestDTO);
    PaginationResponseDTO response = PaginationUtil.pagination(pgDTO.size(), total);
    return response;
  }

  /**
   * Retrieves a list of salutations from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the salutations
   */
  @Override
  public List<RefCodesDTO> getSalutations() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.PREFIX, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of genders from the reference code table, sorted by description in ascending
   * order.
   *
   * @return a list of RefCodesDTO objects containing the genders
   */
  @Override
  public List<RefCodesDTO> getGenders() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.GENDER, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of ID types from the reference code table, sorted by description in ascending
   * order.
   *
   * @return a list of RefCodesDTO objects containing the ID types
   */
  @Override
  public List<RefCodesDTO> getIdTypes() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.ID_TYPE_MAIN, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of other ID types from the reference code table, sorted by description in
   * ascending order. Other ID types are those that are not the main ID type.
   *
   * @return a list of RefCodesDTO objects containing the other ID types
   */
  @Override
  public List<RefCodesDTO> getOtherIdTypes() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.ID_TYPE, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of race reference codes from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the race reference codes
   */
  @Override
  public List<RefCodesDTO> getRaces() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.ETHNICITY, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of religion reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the religion reference codes
   */
  @Override
  public List<RefCodesDTO> getReligions() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.RELIGION, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of nationalities from the PH_SYS_COUNTRYCODE table.
   *
   * @return a list of RefCodesDTO objects containing the ISO country code and corresponding country
   *     name
   */
  @Override
  public List<RefCodesDTO> getNationalities() {
    return utilsRepositoryJooq.getNationalities();
  }

  /**
   * Retrieves a list of payment categories from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the payment categories
   */
  @Override
  public List<RefCodesDTO> getPaymentCategories() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.PAYMENT_CATEGORY, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of government relationship reference codes from the reference code table,
   * sorted by description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the government relationship reference codes
   */
  @Override
  public List<RefCodesDTO> getGovernmentRelationships() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.RELATIONSHIP_GE, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of pensioner categories from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the pensioner category codes and descriptions
   */
  @Override
  public List<RefCodesDTO> getPensionerCategories() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.PENSIONER_TYPE, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of state reference codes from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the state reference codes
   */
  @Override
  public List<RefCodesDTO> getStates() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.STATE_MY, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of marital status reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the marital status reference codes
   */
  @Override
  public List<RefCodesDTO> getMaritalStatus() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.MARITAL_STATUS, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of educational levels from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the educational level codes and descriptions
   */
  @Override
  public List<RefCodesDTO> getEducationalLevels() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.EDUCATION_LEVEL, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of next of kin ID types from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the next of kin ID types
   */
  @Override
  public List<RefCodesDTO> getNextOfKinIdTypes() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.NOK_ID_TYPE, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of relationship reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the relationship reference codes
   */
  @Override
  public List<RefCodesDTO> getRelationships() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.RELATIONSHIP, "rc_desc", "asc");
  }

  /**
   * Retrieves a list of household income reference codes from the reference code table, sorted by
   * code value in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the household income reference codes
   */
  @Override
  public List<RefCodesDTO> getHouseholdIncomes() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.HOUSEHOLD_INCOME, "rc_value", "asc");
  }

  /**
   * Retrieves a list of patient status reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the patient status reference codes
   */
  @Override
  public List<RefCodesDTO> getPatientStatus() {
    return coreUtilsRepositoryJooq.getRefCodes(RefCodeConstant.STATUS, "rc_desc", "asc");
  }

  @Override
  public List<DisciplineSubdisciplineDTO> getDisciplineSubdiscipline() {
    return utilsRepositoryJooq.getDisciplineSubdiscipline();
  }

  @Override
  public List<DoctorListDTO> getDoctorList(Long subdisciplineSeqno) {
    return utilsRepositoryJooq.getDoctorList(subdisciplineSeqno);
  }
}
