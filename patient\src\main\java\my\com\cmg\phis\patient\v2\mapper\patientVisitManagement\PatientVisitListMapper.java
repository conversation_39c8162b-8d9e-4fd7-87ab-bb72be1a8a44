package my.com.cmg.phis.patient.v2.mapper.patientVisitManagement;

import my.com.cmg.phis.core.mapper.ColumnMapper;

public class PatientVisitListMapper extends ColumnMapper {

  public PatientVisitListMapper() {
    COLUMN_MAP.put("patientMrn", "patientMrn");
    COLUMN_MAP.put("patientName", "patientName");
    COLUMN_MAP.put("patientIdNo", "patientIdNo");
    COLUMN_MAP.put("visitDate", "visitDate");
    COLUMN_MAP.put("encounterNo", "encounterNo");
    COLUMN_MAP.put("visitType", "visitType");
    COLUMN_MAP.put("visitTypeDesc", "visitTypeDesc");
    COLUMN_MAP.put("discipline", "discipline");
    COLUMN_MAP.put("subdiscipline", "subdiscipline");
    COLUMN_MAP.put("visitLocation", "visitLocation");
    COLUMN_MAP.put("roomNo", "roomNo");
    COLUMN_MAP.put("bedNo", "bedNo");
  }
}
