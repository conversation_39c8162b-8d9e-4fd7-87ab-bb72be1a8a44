package my.com.cmg.phis.patient.v2.repository;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;
import static org.jooq.impl.DSL.table;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.phis.core.constant.RefCodeConstant;
import my.com.cmg.phis.core.dto.RefCodesDTO;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.repository.Jooq.CoreUtilsRepositoryJooq;
import my.com.cmg.phis.core.util.JooqUtil;
import my.com.cmg.phis.core.util.LogUtil;
import my.com.cmg.phis.core.util.TableUtil;
import my.com.cmg.phis.patient.v2.dto.utils.CityListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListRequestDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DisciplineSubdisciplineDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DoctorListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListRequestDTO;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@AllArgsConstructor
@Slf4j
@Repository
public class UtilsRepositoryJooq {
  private final DSLContext dsl;

  /**
   * Retrieves a list of city records based on the given request criteria.
   *
   * @param requestDTO the request criteria
   * @param pgDTO the pagination request
   * @return a list of city records
   */
  public List<CityListDTO> getCityLists(CityListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    Condition condition = noCondition();
    condition =
        condition.and(field("TC.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition =
        JooqUtil.andCondition(
            condition, field("TC.tc_code"), Field::containsIgnoreCase, requestDTO.townCode());
    condition =
        JooqUtil.andCondition(
            condition,
            field("TC.tc_desc"),
            Field::containsIgnoreCase,
            requestDTO.townDescription());
    condition =
        JooqUtil.andCondition(
            condition, field("TC.state_code"), Field::containsIgnoreCase, requestDTO.stateCode());

    Field<Long> citySeqno = field("TC.tc_seq", Long.class);
    Field<String> townCode = field("TC.tc_code", String.class);
    Field<String> townDescription = field("TC.tc_desc", String.class);
    Field<String> stateCode = field("TC.state_code", String.class);

    Select<?> query =
        dsl.select(citySeqno, townCode, townDescription, stateCode)
            .from(TableUtil.table(TableUtil.PH_TOWN_CODES, "TC"))
            .where(condition)
            .orderBy(CoreUtilsRepositoryJooq.getOrderByField(pgDTO.sort(), pgDTO.sortDirection()))
            .offset((pgDTO.page() - 1) * pgDTO.size())
            .limit(pgDTO.size());
    log.info(LogUtil.QUERY, query);

    List<CityListDTO> result = query.fetchInto(CityListDTO.class);
    return result;
  }

  /**
   * Retrieves the total number of city records that match the given request criteria.
   *
   * @param requestDTO the request criteria containing filters for town code, town description, and
   *     state code
   * @return the total count of city records that satisfy the specified conditions
   */
  public Long getCityListPages(CityListRequestDTO requestDTO) {
    Condition condition = noCondition();
    condition =
        condition.and(field("TC.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition =
        JooqUtil.andCondition(
            condition, field("TC.tc_code"), Field::containsIgnoreCase, requestDTO.townCode());
    condition =
        JooqUtil.andCondition(
            condition,
            field("TC.tc_desc"),
            Field::containsIgnoreCase,
            requestDTO.townDescription());
    condition =
        JooqUtil.andCondition(
            condition, field("TC.state_code"), Field::containsIgnoreCase, requestDTO.stateCode());

    Select<?> query =
        dsl.selectCount().from(TableUtil.table(TableUtil.PH_TOWN_CODES, "TC")).where(condition);
    log.info(LogUtil.QUERY, query);

    Long result = query.fetchOneInto(Long.class);
    return result;
  }

  /**
   * Retrieves a list of occupation records based on the given request criteria.
   *
   * @param requestDTO the request criteria containing filters for occupation code and description
   * @param pgDTO the pagination request
   * @return a list of occupation records
   */
  public List<OccupationListDTO> getOccupationLists(
      OccupationListRequestDTO requestDTO, PaginationRequestDTO pgDTO) {
    Condition condition = noCondition();
    condition =
        condition.and(field("OC.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition =
        JooqUtil.andCondition(
            condition,
            field("OC.occu_code"),
            Field::containsIgnoreCase,
            requestDTO.occupationCode());
    condition =
        JooqUtil.andCondition(
            condition,
            field("OC.occu_desc"),
            Field::containsIgnoreCase,
            requestDTO.occupationDescription());

    Field<Long> occupationSeqno = field("OC.occu_seq", Long.class);
    Field<String> occupationCode = field("OC.occu_code", String.class);
    Field<String> occupationDescription = field("OC.occu_desc", String.class);

    Select<?> query =
        dsl.select(occupationSeqno, occupationCode, occupationDescription)
            .from(TableUtil.table(TableUtil.PH_OCCUPATION, "OC"))
            .where(condition)
            .orderBy(CoreUtilsRepositoryJooq.getOrderByField(pgDTO.sort(), pgDTO.sortDirection()))
            .offset((pgDTO.page() - 1) * pgDTO.size())
            .limit(pgDTO.size());
    log.info(LogUtil.QUERY, query);

    List<OccupationListDTO> result = query.fetchInto(OccupationListDTO.class);
    return result;
  }

  /**
   * Retrieves the total number of occupation records that match the given request criteria.
   *
   * @param requestDTO the request criteria containing filters for occupation code and description
   * @return the total count of occupation records that satisfy the specified conditions
   */
  public Long getOccupationListPages(OccupationListRequestDTO requestDTO) {
    Condition condition = noCondition();
    condition =
        condition.and(field("OC.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition =
        JooqUtil.andCondition(
            condition,
            field("OC.occu_code"),
            Field::containsIgnoreCase,
            requestDTO.occupationCode());
    condition =
        JooqUtil.andCondition(
            condition,
            field("OC.occu_desc"),
            Field::containsIgnoreCase,
            requestDTO.occupationDescription());

    Select<?> query =
        dsl.selectCount().from(TableUtil.table(TableUtil.PH_OCCUPATION, "OC")).where(condition);
    log.info(LogUtil.QUERY, query);

    Long result = query.fetchOneInto(long.class);
    return result;
  }

  /**
   * Retrieves a list of nationalities from the PH_SYS_COUNTRYCODE table.
   *
   * @return a list of RefCodesDTO objects containing the ISO country code and corresponding country
   *     name
   */
  public List<RefCodesDTO> getNationalities() {
    Condition condition = noCondition();
    condition = condition.and(field("active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition = condition.and(field("iso_country_code").isNotNull());

    Field<String> countryCode = field("iso_country_code", String.class);
    Field<String> countryName = field("ccd_name", String.class);

    Select<?> query =
        dsl.select(countryCode, countryName)
            .from(table(TableUtil.PH_SYS_COUNTRYCODE))
            .where(condition)
            .orderBy(field("ccd_name").asc());
    log.info(LogUtil.QUERY, query);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);
    return result;
  }

  public List<DisciplineSubdisciplineDTO> getDisciplineSubdiscipline() {
    Condition condition = noCondition();
    condition =
        condition.and(field("PSD.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition =
        condition.and(field("PD.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));

    Field<Long> disciplineSeqno = field("PSD.discipline_seqno", Long.class);
    Field<Long> subdisciplineSeqno = field("PSD.subdiscipline_seqno", Long.class);
    Field<String> disciplineSubdisciplineName =
        field("PD.discipline_name", String.class)
            .concat("/")
            .concat(field("PSD.subdiscipline_name", String.class));

    Select<?> query =
        dsl.select(disciplineSeqno, subdisciplineSeqno, disciplineSubdisciplineName)
            .from(TableUtil.table(TableUtil.PH_SUB_DISCIPLINES, "PSD"))
            .leftJoin(TableUtil.table(TableUtil.PH_DISCIPLINES, "PD"))
            .on(field("PD.discipline_seqno").eq(field("PSD.discipline_seqno")))
            .where(condition)
            .orderBy(disciplineSubdisciplineName.asc());

    log.info(LogUtil.QUERY, query);

    List<DisciplineSubdisciplineDTO> result = query.fetchInto(DisciplineSubdisciplineDTO.class);
    return result;
  }

  public List<DoctorListDTO> getDoctorList(Long subdisciplineSeqno) {
    Condition condition = noCondition();
    condition =
        condition.and(field("PP.active_flag").eq(RefCodeConstant.ACTIVE_FLAG_TRUE.toString()));
    condition = condition.and(field("PD.speciality").eq(subdisciplineSeqno));

    Field<Long> doctorSeqno = field("PP.prescriber_seqno", Long.class);
    Field<String> doctorName =
        field("PP.first_name", String.class)
            .concat(" ")
            .concat(field("PP.last_name", String.class));
    Field<Long> subSpecialtySeqno = field("PSD.speciality", Long.class);
    Field<String> subSpecialtyName = field("PSD.subdiscipline_name", String.class);

    Select<?> query =
        dsl.select(doctorSeqno, doctorName, subSpecialtySeqno, subSpecialtyName)
            .from(TableUtil.table(TableUtil.PH_PRESCRIBERS, "PP"))
            .leftJoin(TableUtil.table(TableUtil.PH_SUB_DISCIPLINES, "PSD"))
            .on(field("PP.speciality").eq(field("PSD.subdiscipline_seqno")))
            .where(condition)
            .orderBy(doctorName.asc());

    log.info(LogUtil.QUERY, query);

    List<DoctorListDTO> result = query.fetchInto(DoctorListDTO.class);
    return result;
  }
}
