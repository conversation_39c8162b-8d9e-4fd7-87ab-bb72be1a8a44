package my.com.cmg.phis.patient.v2.service.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.security.SecUserDTO;
import my.com.cmg.phis.core.util.PaginationUtil;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListDTO;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListRequestDTO;
import my.com.cmg.phis.patient.v2.mapper.patientVisitManagement.PatientVisitListMapper;
import my.com.cmg.phis.patient.v2.repository.PatientVisitManagementRepositoryJooq;
import my.com.cmg.phis.patient.v2.service.IPatientVisitManagementService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PatientVisitManagementService implements IPatientVisitManagementService {
  private final PatientVisitManagementRepositoryJooq patientVisitManagementRepositoryJooq;

  @Override
  public List<PatientVisitListDTO> getPatientVisitList(
      PatientVisitListRequestDTO requestDTO,
      PaginationRequestDTO paginationRequestDTO,
      SecUserDTO user) {
    PaginationRequestDTO pg =
        PaginationUtil.pageSorting(paginationRequestDTO, new PatientVisitListMapper(), false);

    List<PatientVisitListDTO> response =
        patientVisitManagementRepositoryJooq.getPatientVisitList(requestDTO, pg, user.userId());
    return response;
  }
}
