package my.com.cmg.phis.patient.v2.controller;

import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.phis.core.dto.RefCodesDTO;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.dto.paging.PaginationResponseDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.CityListRequestDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DisciplineSubdisciplineDTO;
import my.com.cmg.phis.patient.v2.dto.utils.DoctorListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListDTO;
import my.com.cmg.phis.patient.v2.dto.utils.OccupationListRequestDTO;
import my.com.cmg.phis.patient.v2.service.IUtilsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController("UtilsControllerV2")
@RequestMapping("/api/v2/patients/utils")
public class UtilsController {
  private final IUtilsService utilsService;

  /**
   * Get a list of cities, filtered by given parameters. The list is pageable and sortable.
   *
   * @param stateCode Filter by state code
   * @param townCode Filter by town code
   * @param townDescription Filter by town description
   * @param sort Sort by
   * @param sortDirection Sort direction
   * @param page Page number
   * @param size Page size
   * @return A list of cities
   */
  @GetMapping("/city")
  public List<CityListDTO> getCityList(
      @RequestParam(required = false) String stateCode,
      @RequestParam(required = false) String townCode,
      @RequestParam(required = false) String townDescription,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size) {
    CityListRequestDTO requestDTO = new CityListRequestDTO(stateCode, townCode, townDescription);
    PaginationRequestDTO paginationRequestDTO =
        new PaginationRequestDTO(sort, sortDirection, page, size);
    return utilsService.getCityLists(requestDTO, paginationRequestDTO);
  }

  /**
   * Get a list of cities, filtered by given parameters. The list is pageable, but not sortable.
   *
   * @param stateCode Filter by state code
   * @param tcCode Filter by town code
   * @param tcDesc Filter by town description
   * @param size Page size
   * @return A PaginationResponseDTO object containing the list of cities, total number of items,
   *     total number of pages, and the current page size
   */
  @GetMapping("/city/page")
  public PaginationResponseDTO getCityListPages(
      @RequestParam(required = false) String stateCode,
      @RequestParam(required = false) String tcCode,
      @RequestParam(required = false) String tcDesc,
      @RequestParam(required = false) Long size) {
    CityListRequestDTO requestDTO = new CityListRequestDTO(stateCode, tcCode, tcDesc);
    PaginationRequestDTO paginationRequestDTO = new PaginationRequestDTO(null, null, null, size);
    return utilsService.getCityListPages(requestDTO, paginationRequestDTO);
  }

  /**
   * Get a list of occupations, filtered by given parameters. The list is pageable and sortable.
   *
   * @param occupationCode Filter by occupation code
   * @param occupationDescription Filter by occupation description
   * @param sort Sort by
   * @param sortDirection Sort direction
   * @param page Page number
   * @param size Page size
   * @return A list of occupations
   */
  @GetMapping("/occupation")
  public List<OccupationListDTO> getOccupationList(
      @RequestParam(required = false) String occupationCode,
      @RequestParam(required = false) String occupationDescription,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size) {
    OccupationListRequestDTO requestDTO =
        new OccupationListRequestDTO(occupationCode, occupationDescription);
    PaginationRequestDTO paginationRequestDTO =
        new PaginationRequestDTO(sort, sortDirection, page, size);
    return utilsService.getOccupationLists(requestDTO, paginationRequestDTO);
  }

  /**
   * Get a list of occupations, filtered by given parameters. The list is pageable but not sortable.
   *
   * @param occupationCode Filter by occupation code
   * @param occupationDescription Filter by occupation description
   * @param size Page size
   * @return A PaginationResponseDTO object containing the list of occupations, total number of
   *     items, total number of pages, and the current page size
   */
  @GetMapping("/occupation/page")
  public PaginationResponseDTO getOccupationListPages(
      @RequestParam(required = false) String occupationCode,
      @RequestParam(required = false) String occupationDescription,
      @RequestParam(required = false) Long size) {
    OccupationListRequestDTO requestDTO =
        new OccupationListRequestDTO(occupationCode, occupationDescription);
    PaginationRequestDTO paginationRequestDTO = new PaginationRequestDTO(null, null, null, size);
    return utilsService.getOccupationListPages(requestDTO, paginationRequestDTO);
  }

  /**
   * Get a list of salutations.
   *
   * @return A list of salutations
   */
  @GetMapping("/salutation")
  public List<RefCodesDTO> getSalutations() {
    return utilsService.getSalutations();
  }

  /**
   * Get a list of gender reference codes.
   *
   * @return A list of RefCodesDTO objects representing gender reference codes
   */
  @GetMapping("/gender")
  public List<RefCodesDTO> getGenders() {
    return utilsService.getGenders();
  }

  /**
   * Get a list of ID types from the reference code table, sorted by description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the ID types
   */
  @GetMapping("/id_type")
  public List<RefCodesDTO> getIdTypes() {
    return utilsService.getIdTypes();
  }

  /**
   * Get a list of other ID types from the reference code table, sorted by description in ascending
   * order. Other ID types are those that are not the main ID type.
   *
   * @return a list of RefCodesDTO objects containing the other ID types
   */
  @GetMapping("/other_id_type")
  public List<RefCodesDTO> getOtherIdTypes() {
    return utilsService.getOtherIdTypes();
  }

  /**
   * Get a list of races from the reference code table, sorted by description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the race codes and descriptions
   */
  @GetMapping("/race")
  public List<RefCodesDTO> getRaces() {
    return utilsService.getRaces();
  }

  /**
   * Get a list of religion reference codes from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the race codes and descriptions
   */
  @GetMapping("/religion")
  public List<RefCodesDTO> getReligions() {
    return utilsService.getReligions();
  }

  /**
   * Get a list of nationalities from the PH_SYS_COUNTRYCODE table.
   *
   * @return a list of RefCodesDTO objects containing the ISO country code and corresponding country
   *     name
   */
  @GetMapping("/nationality")
  public List<RefCodesDTO> getNationalities() {
    return utilsService.getNationalities();
  }

  /**
   * Get a list of payment categories from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the payment category codes and descriptions
   */
  @GetMapping("/payment_category")
  public List<RefCodesDTO> getPaymentCategories() {
    return utilsService.getPaymentCategories();
  }

  /**
   * Get a list of government relationship reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the government relationship codes and
   *     descriptions
   */
  @GetMapping("/government_relationship")
  public List<RefCodesDTO> getGovernmentRelationships() {
    return utilsService.getGovernmentRelationships();
  }

  /**
   * Retrieves a list of pensioner categories from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the pensioner category codes and descriptions
   */
  @GetMapping("/pensioner_category")
  public List<RefCodesDTO> getPensionerCategories() {
    return utilsService.getPensionerCategories();
  }

  /**
   * Retrieves a list of states from the reference code table, sorted by description in ascending
   * order.
   *
   * @return a list of RefCodesDTO objects containing the state reference codes
   */
  @GetMapping("/state")
  public List<RefCodesDTO> getStates() {
    return utilsService.getStates();
  }

  /**
   * Retrieves a list of marital status reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the marital status reference codes
   */
  @GetMapping("/marital_status")
  public List<RefCodesDTO> getMaritalStatus() {
    return utilsService.getMaritalStatus();
  }

  /**
   * Retrieves a list of educational levels from the reference code table, sorted by description in
   * ascending order.
   *
   * @return a list of RefCodesDTO objects containing the educational level codes and descriptions
   */
  @GetMapping("/educational_level")
  public List<RefCodesDTO> getEducationalLevels() {
    return utilsService.getEducationalLevels();
  }

  // @GetMapping("/monthly_household_gross_income")

  /**
   * Retrieves a list of next of kin ID types from the reference code table, sorted by description
   * in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the next of kin ID types
   */
  @GetMapping("/next_of_kin/id_type")
  public List<RefCodesDTO> getNextOfKinIdTypes() {
    return utilsService.getNextOfKinIdTypes();
  }

  /**
   * Retrieves a list of relationship reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the relationship reference codes
   */
  @GetMapping("/relationship")
  public List<RefCodesDTO> getRelationships() {
    return utilsService.getRelationships();
  }

  /**
   * Retrieves a list of household income reference codes from the reference code table, sorted by
   * code value in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the household income reference codes
   */
  @GetMapping("/household_income")
  public List<RefCodesDTO> getHouseholdIncomes() {
    return utilsService.getHouseholdIncomes();
  }

  /**
   * Retrieves a list of patient status reference codes from the reference code table, sorted by
   * description in ascending order.
   *
   * @return a list of RefCodesDTO objects containing the patient status reference codes
   */
  @GetMapping("/patient_status")
  public List<RefCodesDTO> getPatientStatus() {
    return utilsService.getPatientStatus();
  }

  /**
   * Retrieves a list of discipline and subdiscipline from discipline and subdiscipline table,
   * sorted by disciplineSubdisciplineName in ascending order.
   *
   * @return a list of DisciplineSubdisciplineDTO objects containing the discipline and
   *     subdiscipline
   */
  @GetMapping("/discipline_subdiscipline")
  public List<DisciplineSubdisciplineDTO> getDisciplineSubdiscipline() {
    return utilsService.getDisciplineSubdiscipline();
  }

  @GetMapping("/{subdisciplineSeqno}/doctor")
  public List<DoctorListDTO> getDoctorList(
      @PathVariable("subdisciplineSeqno") Long subdisciplineSeqno) {
    return utilsService.getDoctorList(subdisciplineSeqno);
  }
}
