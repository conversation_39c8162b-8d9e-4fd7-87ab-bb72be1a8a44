package my.com.cmg.phis.patient.v2.controller;

import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import my.com.cmg.phis.core.dto.paging.PaginationRequestDTO;
import my.com.cmg.phis.core.security.SecUserDTO;
import my.com.cmg.phis.core.service.ICommonService;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListDTO;
import my.com.cmg.phis.patient.v2.dto.patientVisitManagement.PatientVisitListRequestDTO;
import my.com.cmg.phis.patient.v2.service.IPatientVisitManagementService;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/api/v2/patients/patient_visit_management")
public class PatientVisitManagementController {
  private final IPatientVisitManagementService patientVisitManagementService;
  private final ICommonService commonService;

  @GetMapping("visit_list")
  public List<PatientVisitListDTO> getPatientVisitList(
      @RequestParam(required = false) String patientNameOrIdNo,
      @RequestParam(required = false) String patientMrnOrRn,
      @RequestParam(required = false) Long visitLocation,
      @RequestParam(required = false) LocalDate encounterDateFrom,
      @RequestParam(required = false) LocalDate encounterDateTo,
      @RequestParam(required = false) String disciplineOrSubdiscipline,
      @RequestParam(required = false) String encounterNo,
      @RequestParam(required = false) String roomOrbedNo,
      @RequestParam(required = false) LocalDate visitAdmissionDateFrom,
      @RequestParam(required = false) LocalDate visitAdmissionDateTo,
      @RequestParam(required = false) LocalDate dischargeDate,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size,
      Authentication authentication) {
    PaginationRequestDTO paginationRequestDTO =
        new PaginationRequestDTO(sort, sortDirection, page, size);
    SecUserDTO user = commonService.getAuthenticatedUser(authentication);

    PatientVisitListRequestDTO requestDTO =
        new PatientVisitListRequestDTO(
            patientNameOrIdNo,
            patientMrnOrRn,
            visitLocation,
            encounterDateFrom,
            encounterDateTo,
            disciplineOrSubdiscipline,
            encounterNo,
            roomOrbedNo,
            visitAdmissionDateFrom,
            visitAdmissionDateTo,
            dischargeDate);

    List<PatientVisitListDTO> response =
        patientVisitManagementService.getPatientVisitList(requestDTO, paginationRequestDTO, user);
    return response;
  }
}
